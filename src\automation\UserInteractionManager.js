const inquirer = require('inquirer');
const logger = require('../utils/logger');

/**
 * 用户交互管理器 - 处理用户输入和确认
 */
class UserInteractionManager {
  /**
   * 获取用户输入的提款申请号
   */
  async getUserInput() {
    // 临时硬编码，后续可改为交互式输入
    // return 'LR20230912000005' // 'LR20250512000002'; // 'LR20231101000005'; // 'LR20250512000002'; // 'LR20250515000011'; // LR20250213000014

    logger.separator('─', 60, 'cyan');
    logger.info('📝 准备输入提款申请号');

    const questions = [
      {
        type: 'input',
        name: 'withdrawalNumbers',
        message: '请输入需要查询的提款申请号:',
        prefix: '🔍',
        suffix: ' (多个号码用空格分隔)',
        validate: (input) => {
          const trimmed = input.trim();
          if (!trimmed) {
            return '请输入至少一个提款申请号';
          }
          return true;
        },
        filter: (input) => input.trim(),
      },
    ];

    const answers = await inquirer.prompt(questions);
    return answers.withdrawalNumbers;
  }

  /**
   * 解析和验证提款申请号
   */
  parseWithdrawalNumbers(input) {
    if (!input) return [];

    const numbers = input
      .split(/\s+/)
      .filter((num) => num.trim().length > 0)
      .map((num) => num.trim());

    const uniqueNumbers = [...new Set(numbers)];
    const duplicateCount = numbers.length - uniqueNumbers.length;

    logger.success(`成功解析 ${uniqueNumbers.length} 个提款申请号`);
    if (duplicateCount > 0) {
      logger.warn(`已自动去除 ${duplicateCount} 个重复的申请号`);
    }

    return uniqueNumbers;
  }

  /**
   * 用户确认关闭浏览器
   */
  async confirmBrowserClose() {
    logger.separator('═', 60, 'blue');
    logger.info('🎯 查询任务已完成');

    const questions = [
      {
        type: 'confirm',
        name: 'closeBrowser',
        message: '是否关闭浏览器?',
        prefix: '🔒',
        default: true,
        suffix: ' (选择 No 可以保持浏览器打开以便手动操作)',
      },
    ];

    const { closeBrowser } = await inquirer.prompt(questions);

    if (!closeBrowser) {
      logger.info('用户选择保持浏览器打开');
      await this.showKeepOpenInstructions();
      global.keepBrowserOpen = true;
    }

    logger.separator('═', 60, 'blue');
  }

  /**
   * 错误情况下用户确认关闭浏览器
   */
  async confirmBrowserCloseOnError() {
    logger.separator('═', 60, 'red');
    logger.warn('脚本执行过程中出现错误');

    const questions = [
      {
        type: 'confirm',
        name: 'closeBrowser',
        message: '是否关闭浏览器?',
        prefix: '🔒',
        default: false,
        suffix: ' (选择 No 可以保持浏览器打开以便调试问题)',
      },
    ];

    const { closeBrowser } = await inquirer.prompt(questions);

    if (!closeBrowser) {
      logger.info('用户选择保持浏览器打开以便调试');
      await this.showDebugInstructions();
      global.keepBrowserOpen = true;
    }

    logger.separator('═', 60, 'red');
  }

  /**
   * 显示保持浏览器打开的说明
   */
  async showKeepOpenInstructions() {
    logger.table(
      [
        '查询任务已完成',
        '🌐 浏览器保持打开状态',
        '👆 您可以继续手动操作页面',
        '🔒 完成后请手动关闭浏览器窗口',
      ],
      '温馨提示',
    );

    const continueQuestion = [
      {
        type: 'input',
        name: 'continue',
        message: '按 Enter 键退出脚本 (浏览器将保持打开)',
        prefix: '⏸️',
      },
    ];

    await inquirer.prompt(continueQuestion);
    logger.info('脚本退出，浏览器保持打开状态');
  }

  /**
   * 显示调试说明
   */
  async showDebugInstructions() {
    logger.table(
      [
        '脚本执行出现错误',
        '🌐 浏览器保持打开状态',
        '🔍 您可以检查页面状态进行调试',
        '📸 错误截图已保存',
        '🔒 调试完成后请手动关闭浏览器窗口',
      ],
      '调试提示',
    );
  }
}

module.exports = UserInteractionManager;
